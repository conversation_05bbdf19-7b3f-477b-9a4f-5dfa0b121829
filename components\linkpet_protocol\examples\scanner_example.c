/**
 * @file scanner_example.c
 * @brief LinkPet scanner example
 * 
 * This example demonstrates how to scan for LinkPet devices and display their information.
 * It shows how to:
 * 1. Initialize the LinkPet protocol
 * 2. Scan for devices continuously
 * 3. Parse and display device information
 * 4. Handle scan results
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "linkpet_protocol.h"

static const char *TAG = "LINKPET_SCANNER";

/* Configuration */
#define SCAN_DURATION_MS    10000  // 10 seconds per scan
#define SCAN_INTERVAL_MS    15000  // 15 seconds between scans

/* Device tracking */
#define MAX_DEVICES 10
static linkpet_scan_result_t discovered_devices[MAX_DEVICES];
static int device_count = 0;

/**
 * @brief Check if device is already discovered
 */
static bool is_device_known(const uint8_t *addr)
{
    for (int i = 0; i < device_count; i++) {
        if (memcmp(discovered_devices[i].addr, addr, 6) == 0) {
            return true;
        }
    }
    return false;
}

/**
 * @brief Add or update device in discovered list
 */
static void update_device_list(const linkpet_scan_result_t *result)
{
    /* Check if device already exists */
    for (int i = 0; i < device_count; i++) {
        if (memcmp(discovered_devices[i].addr, result->addr, 6) == 0) {
            /* Update existing device */
            discovered_devices[i] = *result;
            ESP_LOGI(TAG, "Updated device: %s", result->info.device_name);
            return;
        }
    }
    
    /* Add new device if there's space */
    if (device_count < MAX_DEVICES) {
        discovered_devices[device_count] = *result;
        device_count++;
        ESP_LOGI(TAG, "Added new device: %s", result->info.device_name);
    } else {
        ESP_LOGW(TAG, "Device list full, cannot add: %s", result->info.device_name);
    }
}

/**
 * @brief Print device information in a formatted way
 */
static void print_device_info(const linkpet_scan_result_t *result)
{
    ESP_LOGI(TAG, "┌─────────────────────────────────────────────────────────────┐");
    ESP_LOGI(TAG, "│ LinkPet Device Found                                        │");
    ESP_LOGI(TAG, "├─────────────────────────────────────────────────────────────┤");
    ESP_LOGI(TAG, "│ Name:     %-45s │", result->info.device_name);
    ESP_LOGI(TAG, "│ ID:       %-45s │", result->info.device_id);
    ESP_LOGI(TAG, "│ Type:     %-45s │", result->info.device_type);
    ESP_LOGI(TAG, "│ Status:   %-45s │", result->info.status);
    ESP_LOGI(TAG, "│ Battery:  %d%%                                           │", result->info.battery_level);
    ESP_LOGI(TAG, "│ RSSI:     %d dBm                                        │", result->rssi);
    
    if (result->info.latitude != 0.0 || result->info.longitude != 0.0) {
        ESP_LOGI(TAG, "│ Location: %.6f, %.6f                          │", 
                result->info.latitude, result->info.longitude);
    } else {
        ESP_LOGI(TAG, "│ Location: Not available                                     │");
    }
    
    if (result->info.timestamp > 0) {
        ESP_LOGI(TAG, "│ Updated:  %llu                                        │", result->info.timestamp);
    }
    
    if (strlen(result->info.custom_data) > 0) {
        ESP_LOGI(TAG, "│ Custom:   %-45s │", result->info.custom_data);
    }
    
    ESP_LOGI(TAG, "│ MAC:      %02X:%02X:%02X:%02X:%02X:%02X                        │",
            result->addr[5], result->addr[4], result->addr[3],
            result->addr[2], result->addr[1], result->addr[0]);
    ESP_LOGI(TAG, "└─────────────────────────────────────────────────────────────┘");
}

/**
 * @brief Scan callback function
 */
static void scan_callback(const linkpet_scan_result_t *result, void *user_data)
{
    /* Print device information */
    print_device_info(result);
    
    /* Update device list */
    update_device_list(result);
    
    /* Log signal strength category */
    if (result->rssi > -50) {
        ESP_LOGI(TAG, "Signal: Excellent (very close)");
    } else if (result->rssi > -70) {
        ESP_LOGI(TAG, "Signal: Good (nearby)");
    } else if (result->rssi > -85) {
        ESP_LOGI(TAG, "Signal: Fair (moderate distance)");
    } else {
        ESP_LOGI(TAG, "Signal: Poor (far away)");
    }
    
    /* Check for low battery devices */
    if (result->info.battery_level <= 10) {
        ESP_LOGW(TAG, "⚠️  LOW BATTERY WARNING: %s has %d%% battery remaining!", 
                result->info.device_name, result->info.battery_level);
    }
    
    /* Check device status */
    if (strcmp(result->info.status, "lost") == 0) {
        ESP_LOGW(TAG, "🚨 LOST DEVICE ALERT: %s is marked as lost!", result->info.device_name);
    } else if (strcmp(result->info.status, "emergency") == 0) {
        ESP_LOGE(TAG, "🆘 EMERGENCY ALERT: %s needs immediate attention!", result->info.device_name);
    }
}

/**
 * @brief Print summary of discovered devices
 */
static void print_device_summary(void)
{
    ESP_LOGI(TAG, "\n📊 SCAN SUMMARY");
    ESP_LOGI(TAG, "═══════════════════════════════════════════════════════════");
    ESP_LOGI(TAG, "Total devices discovered: %d", device_count);
    
    if (device_count == 0) {
        ESP_LOGI(TAG, "No LinkPet devices found in range.");
        return;
    }
    
    /* Group devices by type */
    int trackers = 0, collars = 0, others = 0;
    int active = 0, inactive = 0, low_battery = 0;
    
    for (int i = 0; i < device_count; i++) {
        /* Count by type */
        if (strcmp(discovered_devices[i].info.device_type, "tracker") == 0) {
            trackers++;
        } else if (strcmp(discovered_devices[i].info.device_type, "collar") == 0) {
            collars++;
        } else {
            others++;
        }
        
        /* Count by status */
        if (strcmp(discovered_devices[i].info.status, "active") == 0) {
            active++;
        } else {
            inactive++;
        }
        
        /* Count low battery */
        if (discovered_devices[i].info.battery_level <= 20) {
            low_battery++;
        }
    }
    
    ESP_LOGI(TAG, "Device types: %d trackers, %d collars, %d others", trackers, collars, others);
    ESP_LOGI(TAG, "Device status: %d active, %d inactive", active, inactive);
    
    if (low_battery > 0) {
        ESP_LOGW(TAG, "⚠️  %d device(s) have low battery!", low_battery);
    }
    
    ESP_LOGI(TAG, "═══════════════════════════════════════════════════════════\n");
}

/**
 * @brief Main scanner task
 */
void linkpet_scanner_task(void *pvParameters)
{
    ESP_LOGI(TAG, "🔍 Starting LinkPet Scanner");
    
    /* Initialize NVS */
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    /* Initialize LinkPet protocol */
    linkpet_err_t err = linkpet_init();
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to initialize LinkPet protocol: %s", linkpet_err_to_str(err));
        vTaskDelete(NULL);
        return;
    }
    
    ESP_LOGI(TAG, "LinkPet protocol initialized successfully");
    ESP_LOGI(TAG, "Starting continuous scan for LinkPet devices...");
    ESP_LOGI(TAG, "Scan duration: %d seconds, interval: %d seconds", 
             SCAN_DURATION_MS / 1000, SCAN_INTERVAL_MS / 1000);
    
    uint32_t scan_count = 0;
    
    /* Main scanning loop */
    while (1) {
        scan_count++;
        ESP_LOGI(TAG, "\n🔍 Starting scan #%lu...", scan_count);
        
        /* Start scanning */
        err = scanlinkpet(SCAN_DURATION_MS, scan_callback, NULL);
        if (err != LINKPET_OK) {
            ESP_LOGE(TAG, "Failed to start scan: %s", linkpet_err_to_str(err));
            vTaskDelay(pdMS_TO_TICKS(5000));
            continue;
        }
        
        /* Wait for scan to complete */
        vTaskDelay(pdMS_TO_TICKS(SCAN_DURATION_MS + 1000));
        
        /* Print summary */
        print_device_summary();
        
        /* Wait before next scan */
        ESP_LOGI(TAG, "Waiting %d seconds before next scan...", 
                (SCAN_INTERVAL_MS - SCAN_DURATION_MS) / 1000);
        vTaskDelay(pdMS_TO_TICKS(SCAN_INTERVAL_MS - SCAN_DURATION_MS));
    }
}

/**
 * @brief Application entry point
 */
void app_main(void)
{
    /* Create the scanner task */
    xTaskCreatePinnedToCore(linkpet_scanner_task, "linkpet_scanner", 
                           8192, NULL, 5, NULL, 1);
}
