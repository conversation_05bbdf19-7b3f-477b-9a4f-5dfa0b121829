# LinkPet Protocol Library

A comprehensive BLE-based protocol library for LinkPet devices using ESP-IDF and NimBLE Extended Advertising.

## Overview

The LinkPet Protocol provides four main interfaces for device communication:
- `scanlinkpet` - Scan for LinkPet devices
- `startadv` - Start advertising LinkPet information
- `stopadv` - Stop advertising
- `setinfo` - Set/update the information to be advertised

## Features

- **NimBLE Extended Advertising**: Efficient BLE 5.0 advertising with JSON data
- **GATT Service**: External configuration via GATT characteristics
- **JSON Data Format**: Structured, extensible device information
- **Thread-Safe**: Mutex-protected operations
- **Comprehensive Error Handling**: Detailed error codes and validation
- **Configurable Parameters**: Customizable advertising intervals, TX power, etc.

## JSON Data Schema

```json
{
  "device_id": "string (required)",
  "device_name": "string (required)", 
  "device_type": "string (optional, default: 'tracker')",
  "status": "string (optional, default: 'active')",
  "battery_level": "number (0-100, optional, default: 100)",
  "latitude": "number (-90 to 90, optional)",
  "longitude": "number (-180 to 180, optional)",
  "timestamp": "number (Unix timestamp, optional)",
  "custom_data": "object (optional)"
}
```

## GATT Service

- **Service UUID**: `6E400001-B5A3-F393-E0A9-E50E24DCCA9E`
- **Configuration Characteristic**: `6E400002-B5A3-F393-E0A9-E50E24DCCA9E` (Write)
- **Status Characteristic**: `6E400003-B5A3-F393-E0A9-E50E24DCCA9E` (Read/Notify)

## API Reference

### Initialization

```c
#include "linkpet_protocol.h"

// Initialize the LinkPet protocol
linkpet_err_t err = linkpet_init();
if (err != LINKPET_OK) {
    ESP_LOGE(TAG, "Failed to initialize LinkPet: %s", linkpet_err_to_str(err));
}
```

### Setting Device Information

```c
linkpet_device_info_t info = {
    .device_id = "pet_tracker_001",
    .device_name = "Fluffy's Tracker",
    .device_type = "collar",
    .status = "active",
    .battery_level = 85,
    .latitude = 37.7749,
    .longitude = -122.4194,
    .timestamp = 1640995200,
    .custom_data = "{\"owner\":\"John Doe\"}"
};

linkpet_err_t err = setinfo(&info);
if (err != LINKPET_OK) {
    ESP_LOGE(TAG, "Failed to set device info: %s", linkpet_err_to_str(err));
}
```

### Starting Advertising

```c
// Use default parameters
linkpet_err_t err = startadv(NULL);

// Or specify custom parameters
linkpet_adv_params_t params = {
    .adv_int_min = 160,    // 100ms
    .adv_int_max = 160,    // 100ms
    .tx_power = 0,         // 0 dBm
    .connectable = true,
    .scannable = true
};
err = startadv(&params);

if (err != LINKPET_OK) {
    ESP_LOGE(TAG, "Failed to start advertising: %s", linkpet_err_to_str(err));
}
```

### Scanning for Devices

```c
void scan_callback(const linkpet_scan_result_t *result, void *user_data)
{
    ESP_LOGI(TAG, "Found device: %s (RSSI: %d dBm)", 
             result->info.device_name, result->rssi);
    ESP_LOGI(TAG, "  ID: %s", result->info.device_id);
    ESP_LOGI(TAG, "  Type: %s", result->info.device_type);
    ESP_LOGI(TAG, "  Status: %s", result->info.status);
    ESP_LOGI(TAG, "  Battery: %d%%", result->info.battery_level);
    ESP_LOGI(TAG, "  Location: %.6f, %.6f", 
             result->info.latitude, result->info.longitude);
}

// Scan for 10 seconds
linkpet_err_t err = scanlinkpet(10000, scan_callback, NULL);
if (err != LINKPET_OK) {
    ESP_LOGE(TAG, "Failed to start scan: %s", linkpet_err_to_str(err));
}
```

### GATT Update Callback

```c
void gatt_update_callback(const linkpet_device_info_t *info, void *user_data)
{
    ESP_LOGI(TAG, "Device info updated via GATT: %s", info->device_name);
    // Handle the updated information
}

linkpet_set_gatt_callback(gatt_update_callback, NULL);
```

### Stopping Advertising

```c
linkpet_err_t err = stopadv();
if (err != LINKPET_OK) {
    ESP_LOGE(TAG, "Failed to stop advertising: %s", linkpet_err_to_str(err));
}
```

### Cleanup

```c
linkpet_err_t err = linkpet_deinit();
if (err != LINKPET_OK) {
    ESP_LOGE(TAG, "Failed to deinitialize LinkPet: %s", linkpet_err_to_str(err));
}
```

## Error Handling

All functions return `linkpet_err_t` error codes:

- `LINKPET_OK` - Success
- `LINKPET_ERR_INVALID_ARG` - Invalid argument
- `LINKPET_ERR_NO_MEM` - Out of memory
- `LINKPET_ERR_INVALID_STATE` - Invalid state
- `LINKPET_ERR_NOT_FOUND` - Device not found
- `LINKPET_ERR_TIMEOUT` - Operation timeout
- `LINKPET_ERR_ADVERTISING_ACTIVE` - Advertising already active
- `LINKPET_ERR_ADVERTISING_INACTIVE` - Advertising not active
- `LINKPET_ERR_JSON_PARSE` - JSON parsing error
- `LINKPET_ERR_JSON_FORMAT` - Invalid JSON format
- `LINKPET_ERR_BLE_INIT` - BLE initialization error
- `LINKPET_ERR_GATT_ERROR` - GATT operation error

Use `linkpet_err_to_str()` to convert error codes to human-readable strings.

## Configuration

### CMakeLists.txt

Add the LinkPet protocol component to your project:

```cmake
idf_component_register(
    SRCS "main.c"
    INCLUDE_DIRS "."
    PRIV_REQUIRES linkpet_protocol
)
```

### sdkconfig

Enable required components in your project configuration:

```
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_EXT_ADV=y
CONFIG_BT_NIMBLE_MAX_CONNECTIONS=1
CONFIG_BT_NIMBLE_ROLE_CENTRAL=y
CONFIG_BT_NIMBLE_ROLE_PERIPHERAL=y
CONFIG_BT_NIMBLE_ROLE_BROADCASTER=y
CONFIG_BT_NIMBLE_ROLE_OBSERVER=y
```

## Thread Safety

The LinkPet protocol is thread-safe. All public functions use mutex protection to ensure safe concurrent access.

## Memory Usage

- Static memory: ~2KB for protocol state
- Dynamic memory: Minimal, mainly for JSON parsing
- Advertising data: Up to 251 bytes for extended advertising

## Limitations

- Maximum JSON payload: 200 bytes
- Maximum advertising data: 251 bytes (BLE extended advertising)
- Single concurrent scan operation
- GPS coordinates validated to standard ranges (-90/90, -180/180)

## Examples

See the `examples/` directory for complete usage examples including:
- Basic device tracker
- Pet collar with GPS
- Central scanner application
- GATT configuration client
