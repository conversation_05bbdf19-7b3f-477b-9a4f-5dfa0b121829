#ifndef LINKPET_INTERNAL_H
#define LINKPET_INTERNAL_H

#include "linkpet_protocol.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include "cJSON.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief LinkPet Protocol Constants
 */
#define LINKPET_SERVICE_UUID        0x1234
#define LINKPET_CONFIG_CHAR_UUID    0x1235
#define LINKPET_STATUS_CHAR_UUID    0x1236

#define LINKPET_MAX_ADV_DATA_LEN    251
#define LINKPET_MAX_JSON_LEN        200
#define LINKPET_DEVICE_NAME         "LinkPet"

/**
 * @brief LinkPet Protocol State
 */
typedef struct {
    bool initialized;
    bool advertising;
    bool scanning;
    linkpet_device_info_t current_info;
    linkpet_adv_params_t adv_params;
    linkpet_scan_cb_t scan_callback;
    void *scan_user_data;
    linkpet_gatt_update_cb_t gatt_callback;
    void *gatt_user_data;
    uint16_t conn_handle;
    uint8_t adv_data[LINKPET_MAX_ADV_DATA_LEN];
    size_t adv_data_len;
} linkpet_state_t;

/**
 * @brief Get LinkPet protocol state
 * 
 * @return linkpet_state_t* Pointer to protocol state
 */
linkpet_state_t* linkpet_get_state(void);

/**
 * @brief Convert device info to JSON string
 * 
 * @param info Device information
 * @param json_str Output JSON string buffer
 * @param max_len Maximum length of JSON string
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_info_to_json(const linkpet_device_info_t *info, char *json_str, size_t max_len);

/**
 * @brief Parse JSON string to device info
 * 
 * @param json_str JSON string
 * @param info Output device information
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_json_to_info(const char *json_str, linkpet_device_info_t *info);

/**
 * @brief Build advertising data from device info
 * 
 * @param info Device information
 * @param adv_data Output advertising data buffer
 * @param max_len Maximum length of advertising data
 * @param actual_len Actual length of advertising data
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_build_adv_data(const linkpet_device_info_t *info, uint8_t *adv_data, 
                                     size_t max_len, size_t *actual_len);

/**
 * @brief Parse advertising data to device info
 * 
 * @param adv_data Advertising data
 * @param adv_len Length of advertising data
 * @param info Output device information
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_parse_adv_data(const uint8_t *adv_data, size_t adv_len, 
                                     linkpet_device_info_t *info);

/* Internal function declarations for modular components */

/**
 * @brief Initialize advertising module
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_advertising_init(void);

/**
 * @brief Deinitialize advertising module
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_advertising_deinit(void);

/**
 * @brief Initialize GATT module
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_gatt_init(void);

/**
 * @brief Deinitialize GATT module
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_gatt_deinit(void);

/**
 * @brief Initialize scanner module
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_scanner_init(void);

/**
 * @brief Deinitialize scanner module
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_scanner_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* LINKPET_INTERNAL_H */
