#ifndef LINKPET_PROTOCOL_H
#define LINKPET_PROTOCOL_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief LinkPet Protocol Error Codes
 */
typedef enum {
    LINKPET_OK = 0,                    ///< Success
    LINKPET_ERR_INVALID_ARG,           ///< Invalid argument
    LINKPET_ERR_NO_MEM,                ///< Out of memory
    LINKPET_ERR_INVALID_STATE,         ///< Invalid state
    LINKPET_ERR_NOT_FOUND,             ///< Device not found
    LINKPET_ERR_TIMEOUT,               ///< Operation timeout
    LINKPET_ERR_ADVERTISING_ACTIVE,    ///< Advertising already active
    LINKPET_ERR_ADVERTISING_INACTIVE,  ///< Advertising not active
    LINKPET_ERR_JSON_PARSE,            ///< JSON parsing error
    LINKPET_ERR_JSON_FORMAT,           ///< Invalid JSON format
    LINKPET_ERR_BLE_INIT,              ///< BLE initialization error
    LINKPET_ERR_GATT_ERROR,            ///< GATT operation error
} linkpet_err_t;

/**
 * @brief LinkPet Device Information Structure
 */
typedef struct {
    char device_id[32];        ///< Unique device identifier
    char device_name[32];      ///< Human readable device name
    char device_type[16];      ///< Device type (e.g., "pet_tracker", "collar")
    char status[16];           ///< Device status (e.g., "active", "sleeping", "lost")
    uint8_t battery_level;     ///< Battery level (0-100)
    float latitude;            ///< GPS latitude
    float longitude;           ///< GPS longitude
    uint64_t timestamp;        ///< Unix timestamp
    char custom_data[128];     ///< Custom JSON data
} linkpet_device_info_t;

/**
 * @brief LinkPet Scan Result Structure
 */
typedef struct {
    uint8_t addr[6];           ///< Device MAC address
    int8_t rssi;               ///< Signal strength
    linkpet_device_info_t info; ///< Parsed device information
} linkpet_scan_result_t;

/**
 * @brief LinkPet Advertising Parameters
 */
typedef struct {
    uint16_t adv_int_min;      ///< Minimum advertising interval (units of 0.625ms)
    uint16_t adv_int_max;      ///< Maximum advertising interval (units of 0.625ms)
    int8_t tx_power;           ///< Transmission power (dBm)
    bool connectable;          ///< Whether device is connectable
    bool scannable;            ///< Whether device is scannable
} linkpet_adv_params_t;

/**
 * @brief Scan callback function type
 * 
 * @param result Pointer to scan result
 * @param user_data User data passed to scan function
 */
typedef void (*linkpet_scan_cb_t)(const linkpet_scan_result_t *result, void *user_data);

/**
 * @brief GATT update callback function type
 * 
 * @param info Updated device information
 * @param user_data User data
 */
typedef void (*linkpet_gatt_update_cb_t)(const linkpet_device_info_t *info, void *user_data);

/**
 * @brief Initialize LinkPet protocol
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_init(void);

/**
 * @brief Deinitialize LinkPet protocol
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_deinit(void);

/**
 * @brief Scan for LinkPet devices
 * 
 * @param duration_ms Scan duration in milliseconds
 * @param callback Callback function for scan results
 * @param user_data User data passed to callback
 * @return linkpet_err_t Error code
 */
linkpet_err_t scanlinkpet(uint32_t duration_ms, linkpet_scan_cb_t callback, void *user_data);

/**
 * @brief Start advertising LinkPet information
 * 
 * @param params Advertising parameters (NULL for default)
 * @return linkpet_err_t Error code
 */
linkpet_err_t startadv(const linkpet_adv_params_t *params);

/**
 * @brief Stop advertising
 * 
 * @return linkpet_err_t Error code
 */
linkpet_err_t stopadv(void);

/**
 * @brief Set/update the information to be advertised
 * 
 * @param info Device information to advertise
 * @return linkpet_err_t Error code
 */
linkpet_err_t setinfo(const linkpet_device_info_t *info);

/**
 * @brief Get current device information
 * 
 * @param info Pointer to store current device information
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_get_info(linkpet_device_info_t *info);

/**
 * @brief Check if advertising is active
 * 
 * @return true if advertising is active, false otherwise
 */
bool linkpet_is_advertising(void);

/**
 * @brief Set GATT update callback
 * 
 * @param callback Callback function for GATT updates
 * @param user_data User data passed to callback
 * @return linkpet_err_t Error code
 */
linkpet_err_t linkpet_set_gatt_callback(linkpet_gatt_update_cb_t callback, void *user_data);

/**
 * @brief Convert LinkPet error to string
 * 
 * @param err Error code
 * @return const char* Error string
 */
const char* linkpet_err_to_str(linkpet_err_t err);

#ifdef __cplusplus
}
#endif

#endif /* LINKPET_PROTOCOL_H */
