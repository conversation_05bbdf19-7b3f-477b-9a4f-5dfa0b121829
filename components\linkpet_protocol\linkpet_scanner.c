#include "linkpet_internal.h"
#include "esp_log.h"
#include "host/ble_hs.h"
#include "host/ble_gap.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"
#include <string.h>

static const char *TAG = "LINKPET_SCANNER";

static TimerHandle_t scan_timer = NULL;
static bool scan_active = false;

static int linkpet_scan_event(struct ble_gap_event *event, void *arg);
static void scan_timeout_callback(TimerHandle_t timer);

linkpet_err_t linkpet_scanner_init(void)
{
    ESP_LOGI(TAG, "Scanner module initialized");
    return LINKPET_OK;
}

linkpet_err_t linkpet_scanner_deinit(void)
{
    if (scan_active) {
        ble_gap_disc_cancel();
        scan_active = false;
    }
    
    if (scan_timer) {
        xTimerDelete(scan_timer, portMAX_DELAY);
        scan_timer = NULL;
    }
    
    ESP_LOGI(TAG, "Scanner module deinitialized");
    return LINKPET_OK;
}

static void scan_timeout_callback(TimerHandle_t timer)
{
    ESP_LOGI(TAG, "Scan timeout, stopping scan");
    
    if (scan_active) {
        ble_gap_disc_cancel();
        scan_active = false;
    }
}

static int linkpet_scan_event(struct ble_gap_event *event, void *arg)
{
    linkpet_state_t *state = linkpet_get_state();
    
    switch (event->type) {
    case BLE_GAP_EVENT_DISC:
        {
            /* Parse advertising data to check if it's a LinkPet device */
            linkpet_device_info_t info;
            linkpet_err_t err = linkpet_parse_adv_data(event->disc.data, event->disc.length_data, &info);
            
            if (err == LINKPET_OK && state->scan_callback) {
                /* Create scan result */
                linkpet_scan_result_t result;
                memcpy(result.addr, event->disc.addr.val, 6);
                result.rssi = event->disc.rssi;
                result.info = info;
                
                /* Call user callback */
                state->scan_callback(&result, state->scan_user_data);
            }
        }
        break;
        
    case BLE_GAP_EVENT_DISC_COMPLETE:
        ESP_LOGI(TAG, "Discovery complete; reason=%d", event->disc_complete.reason);
        scan_active = false;
        
        /* Stop the timer */
        if (scan_timer) {
            xTimerStop(scan_timer, 0);
        }
        break;
        
    default:
        break;
    }
    
    return 0;
}

linkpet_err_t scanlinkpet(uint32_t duration_ms, linkpet_scan_cb_t callback, void *user_data)
{
    linkpet_state_t *state = linkpet_get_state();
    
    if (!state->initialized) {
        return LINKPET_ERR_INVALID_STATE;
    }
    
    if (scan_active) {
        ESP_LOGW(TAG, "Scan already active");
        return LINKPET_ERR_INVALID_STATE;
    }
    
    if (callback == NULL) {
        return LINKPET_ERR_INVALID_ARG;
    }
    
    /* Store callback and user data */
    state->scan_callback = callback;
    state->scan_user_data = user_data;
    
    /* Create or update scan timer */
    if (scan_timer == NULL) {
        scan_timer = xTimerCreate("scan_timer", pdMS_TO_TICKS(duration_ms), 
                                 pdFALSE, NULL, scan_timeout_callback);
        if (scan_timer == NULL) {
            ESP_LOGE(TAG, "Failed to create scan timer");
            return LINKPET_ERR_NO_MEM;
        }
    } else {
        xTimerChangePeriod(scan_timer, pdMS_TO_TICKS(duration_ms), 0);
    }
    
    /* Configure scan parameters */
    struct ble_gap_disc_params disc_params = {0};
    disc_params.filter_duplicates = 1;
    disc_params.passive = 0;  // Active scanning
    disc_params.itvl = 0x10;  // 10ms
    disc_params.window = 0x10; // 10ms
    disc_params.filter_policy = 0;
    disc_params.limited = 0;
    
    /* Start scanning */
    int rc = ble_gap_disc(BLE_OWN_ADDR_PUBLIC, BLE_HS_FOREVER, &disc_params,
                         linkpet_scan_event, NULL);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to start scanning: %d", rc);
        return LINKPET_ERR_BLE_INIT;
    }
    
    /* Start the timeout timer */
    xTimerStart(scan_timer, 0);
    scan_active = true;
    
    ESP_LOGI(TAG, "Started scanning for %lu ms", duration_ms);
    return LINKPET_OK;
}
