# Load esp32c6 ROM ELF symbols
define target hookpost-remote
set confirm off
  # if $_streq((char *) 0x4004a798, "Sep 19 2022")
  if (*(int*) 0x4004a798) == 0x20706553 && (*(int*) 0x4004a79c) == 0x32203931 && (*(int*) 0x4004a7a0) == 0x323230
    add-symbol-file D:/Espressif/tools/esp-rom-elfs/20230320/esp32c6_rev0_rom.elf
  else
    echo Warning: Unknown esp32c6 ROM revision.\n
  end
set confirm on
end


# Load bootloader symbols
set confirm off
    add-symbol-file E:/APPprj/LinkPet/linkpet-protocol/build/bootloader/bootloader.elf
set confirm on

# Load application symbols
file E:/APPprj/LinkPet/linkpet-protocol/build/04_SD_Card.elf
