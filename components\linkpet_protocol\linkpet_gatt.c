#include "linkpet_internal.h"
#include "esp_log.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include <string.h>

static const char *TAG = "LINKPET_GATT";

/* LinkPet GATT Service UUID: 6E400001-B5A3-F393-E0A9-E50E24DCCA9E */
static const ble_uuid128_t linkpet_service_uuid = 
    BLE_UUID128_INIT(0x9e, 0xca, 0xdc, 0x24, 0x0e, 0xe5, 0xa9, 0xe0,
                     0x93, 0xf3, 0xa3, 0xb5, 0x01, 0x00, 0x40, 0x6e);

/* Configuration Characteristic UUID: 6E400002-B5A3-F393-E0A9-E50E24DCCA9E */
static const ble_uuid128_t linkpet_config_char_uuid = 
    BLE_UUID128_INIT(0x9e, 0xca, 0xdc, 0x24, 0x0e, 0xe5, 0xa9, 0xe0,
                     0x93, 0xf3, 0xa3, 0xb5, 0x02, 0x00, 0x40, 0x6e);

/* Status Characteristic UUID: 6E400003-B5A3-F393-E0A9-E50E24DCCA9E */
static const ble_uuid128_t linkpet_status_char_uuid = 
    BLE_UUID128_INIT(0x9e, 0xca, 0xdc, 0x24, 0x0e, 0xe5, 0xa9, 0xe0,
                     0x93, 0xf3, 0xa3, 0xb5, 0x03, 0x00, 0x40, 0x6e);

static uint16_t config_char_handle;
static uint16_t status_char_handle;

static int linkpet_gatt_access(uint16_t conn_handle, uint16_t attr_handle,
                              struct ble_gatt_access_ctxt *ctxt, void *arg);

/* GATT service definition */
static const struct ble_gatt_svc_def linkpet_gatt_svcs[] = {
    {
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &linkpet_service_uuid.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                /* Configuration Characteristic - Write */
                .uuid = &linkpet_config_char_uuid.u,
                .access_cb = linkpet_gatt_access,
                .flags = BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_WRITE_NO_RSP,
                .val_handle = &config_char_handle,
            },
            {
                /* Status Characteristic - Read/Notify */
                .uuid = &linkpet_status_char_uuid.u,
                .access_cb = linkpet_gatt_access,
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &status_char_handle,
            },
            {
                0, /* No more characteristics */
            }
        }
    },
    {
        0, /* No more services */
    }
};

static int linkpet_gatt_access(uint16_t conn_handle, uint16_t attr_handle,
                              struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    linkpet_state_t *state = linkpet_get_state();
    int rc = 0;
    
    if (attr_handle == config_char_handle) {
        /* Configuration characteristic */
        switch (ctxt->op) {
        case BLE_GATT_ACCESS_OP_WRITE_CHR:
            {
                /* Receive JSON configuration data */
                uint16_t data_len = OS_MBUF_PKTLEN(ctxt->om);
                if (data_len >= LINKPET_MAX_JSON_LEN) {
                    ESP_LOGE(TAG, "Configuration data too large: %d bytes", data_len);
                    return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
                }
                
                char json_str[LINKPET_MAX_JSON_LEN];
                rc = ble_hs_mbuf_to_flat(ctxt->om, json_str, data_len, NULL);
                if (rc != 0) {
                    ESP_LOGE(TAG, "Failed to copy configuration data");
                    return BLE_ATT_ERR_UNLIKELY;
                }
                json_str[data_len] = '\0';
                
                ESP_LOGI(TAG, "Received configuration: %s", json_str);
                
                /* Parse JSON and update device info */
                linkpet_device_info_t new_info;
                linkpet_err_t err = linkpet_json_to_info(json_str, &new_info);
                if (err != LINKPET_OK) {
                    ESP_LOGE(TAG, "Failed to parse configuration JSON: %s", linkpet_err_to_str(err));
                    return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
                }
                
                /* Update device info */
                err = setinfo(&new_info);
                if (err != LINKPET_OK) {
                    ESP_LOGE(TAG, "Failed to update device info: %s", linkpet_err_to_str(err));
                    return BLE_ATT_ERR_UNLIKELY;
                }
                
                /* Notify status characteristic if notifications are enabled */
                if (state->conn_handle != BLE_HS_CONN_HANDLE_NONE) {
                    char status_json[LINKPET_MAX_JSON_LEN];
                    err = linkpet_info_to_json(&new_info, status_json, sizeof(status_json));
                    if (err == LINKPET_OK) {
                        struct os_mbuf *om = ble_hs_mbuf_from_flat(status_json, strlen(status_json));
                        if (om != NULL) {
                            ble_gattc_notify_custom(state->conn_handle, status_char_handle, om);
                        }
                    }
                }
                
                /* Call user callback if set */
                if (state->gatt_callback) {
                    state->gatt_callback(&new_info, state->gatt_user_data);
                }
                
                ESP_LOGI(TAG, "Configuration updated successfully");
            }
            break;
            
        default:
            rc = BLE_ATT_ERR_UNLIKELY;
            break;
        }
    } else if (attr_handle == status_char_handle) {
        /* Status characteristic */
        switch (ctxt->op) {
        case BLE_GATT_ACCESS_OP_READ_CHR:
            {
                /* Return current device info as JSON */
                char json_str[LINKPET_MAX_JSON_LEN];
                linkpet_err_t err = linkpet_info_to_json(&state->current_info, json_str, sizeof(json_str));
                if (err != LINKPET_OK) {
                    ESP_LOGE(TAG, "Failed to convert device info to JSON");
                    return BLE_ATT_ERR_UNLIKELY;
                }
                
                rc = os_mbuf_append(ctxt->om, json_str, strlen(json_str));
                if (rc != 0) {
                    ESP_LOGE(TAG, "Failed to append status data");
                    return BLE_ATT_ERR_INSUFFICIENT_RES;
                }
                
                ESP_LOGI(TAG, "Status read: %s", json_str);
            }
            break;
            
        default:
            rc = BLE_ATT_ERR_UNLIKELY;
            break;
        }
    } else {
        rc = BLE_ATT_ERR_UNLIKELY;
    }
    
    return rc;
}

linkpet_err_t linkpet_gatt_init(void)
{
    int rc;
    
    /* Initialize GATT services */
    ble_svc_gap_init();
    ble_svc_gatt_init();
    
    /* Register LinkPet GATT service */
    rc = ble_gatts_count_cfg(linkpet_gatt_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to count GATT configuration: %d", rc);
        return LINKPET_ERR_GATT_ERROR;
    }
    
    rc = ble_gatts_add_svcs(linkpet_gatt_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to add GATT services: %d", rc);
        return LINKPET_ERR_GATT_ERROR;
    }
    
    ESP_LOGI(TAG, "GATT service initialized successfully");
    ESP_LOGI(TAG, "Config characteristic handle: %d", config_char_handle);
    ESP_LOGI(TAG, "Status characteristic handle: %d", status_char_handle);
    
    return LINKPET_OK;
}

linkpet_err_t linkpet_gatt_deinit(void)
{
    ESP_LOGI(TAG, "GATT service deinitialized");
    return LINKPET_OK;
}
