/**
 * @file basic_tracker.c
 * @brief Basic LinkPet tracker example
 * 
 * This example demonstrates how to use the LinkPet protocol to:
 * 1. Initialize the protocol
 * 2. Set device information
 * 3. Start advertising
 * 4. Handle GATT updates
 * 5. Periodically update location and battery
 */

#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "linkpet_protocol.h"

static const char *TAG = "LINKPET_EXAMPLE";

/* Device configuration */
#define DEVICE_ID           "tracker_001"
#define DEVICE_NAME         "Pet Tracker Demo"
#define DEVICE_TYPE         "collar"
#define UPDATE_INTERVAL_MS  30000  // Update every 30 seconds

/* Global state */
static linkpet_device_info_t g_device_info;
static TimerHandle_t update_timer;
static bool protocol_initialized = false;

/**
 * @brief Simulate GPS location update
 */
static void update_gps_location(linkpet_device_info_t *info)
{
    /* Simulate movement around San Francisco */
    static float base_lat = 37.7749;
    static float base_lon = -122.4194;
    static int step = 0;
    
    /* Simple circular movement simulation */
    float radius = 0.001; // ~100m radius
    float angle = (step * 0.1); // Slow movement
    
    info->latitude = base_lat + radius * cos(angle);
    info->longitude = base_lon + radius * sin(angle);
    
    step++;
    
    ESP_LOGI(TAG, "GPS updated: %.6f, %.6f", info->latitude, info->longitude);
}

/**
 * @brief Simulate battery level update
 */
static void update_battery_level(linkpet_device_info_t *info)
{
    /* Simulate battery drain */
    static uint8_t battery_drain_counter = 0;
    
    battery_drain_counter++;
    if (battery_drain_counter >= 10) { // Drain 1% every 10 updates
        if (info->battery_level > 0) {
            info->battery_level--;
        }
        battery_drain_counter = 0;
        ESP_LOGI(TAG, "Battery level: %d%%", info->battery_level);
    }
}

/**
 * @brief Update device status based on battery level
 */
static void update_device_status(linkpet_device_info_t *info)
{
    if (info->battery_level <= 10) {
        strcpy(info->status, "low_battery");
    } else if (info->battery_level <= 30) {
        strcpy(info->status, "battery_warning");
    } else {
        strcpy(info->status, "active");
    }
}

/**
 * @brief Timer callback for periodic updates
 */
static void update_timer_callback(TimerHandle_t timer)
{
    if (!protocol_initialized) {
        return;
    }
    
    /* Update device information */
    update_gps_location(&g_device_info);
    update_battery_level(&g_device_info);
    update_device_status(&g_device_info);
    
    /* Update timestamp */
    g_device_info.timestamp = esp_timer_get_time() / 1000000; // Convert to seconds
    
    /* Update custom data with additional info */
    snprintf(g_device_info.custom_data, sizeof(g_device_info.custom_data),
             "{\"temperature\":%.1f,\"activity\":\"walking\"}", 
             20.0 + (esp_random() % 100) / 10.0); // Random temperature 20-30°C
    
    /* Apply the updates */
    linkpet_err_t err = setinfo(&g_device_info);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to update device info: %s", linkpet_err_to_str(err));
    } else {
        ESP_LOGI(TAG, "Device info updated successfully");
    }
}

/**
 * @brief GATT update callback - called when device info is updated via GATT
 */
static void gatt_update_callback(const linkpet_device_info_t *info, void *user_data)
{
    ESP_LOGI(TAG, "Device configuration updated via GATT:");
    ESP_LOGI(TAG, "  Device Name: %s", info->device_name);
    ESP_LOGI(TAG, "  Device Type: %s", info->device_type);
    ESP_LOGI(TAG, "  Status: %s", info->status);
    ESP_LOGI(TAG, "  Battery: %d%%", info->battery_level);
    ESP_LOGI(TAG, "  Location: %.6f, %.6f", info->latitude, info->longitude);
    ESP_LOGI(TAG, "  Custom Data: %s", info->custom_data);
    
    /* Update our local copy */
    memcpy(&g_device_info, info, sizeof(linkpet_device_info_t));
}

/**
 * @brief Initialize device information
 */
static void init_device_info(void)
{
    memset(&g_device_info, 0, sizeof(linkpet_device_info_t));
    
    strcpy(g_device_info.device_id, DEVICE_ID);
    strcpy(g_device_info.device_name, DEVICE_NAME);
    strcpy(g_device_info.device_type, DEVICE_TYPE);
    strcpy(g_device_info.status, "active");
    
    g_device_info.battery_level = 100;
    g_device_info.latitude = 37.7749;   // San Francisco
    g_device_info.longitude = -122.4194;
    g_device_info.timestamp = esp_timer_get_time() / 1000000;
    
    strcpy(g_device_info.custom_data, "{\"owner\":\"Demo User\",\"breed\":\"Golden Retriever\"}");
}

/**
 * @brief Main application task
 */
void linkpet_tracker_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Starting LinkPet Tracker Demo");
    
    /* Initialize NVS */
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    /* Initialize device information */
    init_device_info();
    
    /* Initialize LinkPet protocol */
    linkpet_err_t err = linkpet_init();
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to initialize LinkPet protocol: %s", linkpet_err_to_str(err));
        vTaskDelete(NULL);
        return;
    }
    
    protocol_initialized = true;
    ESP_LOGI(TAG, "LinkPet protocol initialized successfully");
    
    /* Set GATT update callback */
    err = linkpet_set_gatt_callback(gatt_update_callback, NULL);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to set GATT callback: %s", linkpet_err_to_str(err));
    }
    
    /* Set initial device information */
    err = setinfo(&g_device_info);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to set device info: %s", linkpet_err_to_str(err));
        linkpet_deinit();
        vTaskDelete(NULL);
        return;
    }
    
    /* Configure advertising parameters */
    linkpet_adv_params_t adv_params = {
        .adv_int_min = 160,    // 100ms
        .adv_int_max = 160,    // 100ms
        .tx_power = 0,         // 0 dBm
        .connectable = true,   // Allow GATT connections
        .scannable = true      // Allow scan responses
    };
    
    /* Start advertising */
    err = startadv(&adv_params);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to start advertising: %s", linkpet_err_to_str(err));
        linkpet_deinit();
        vTaskDelete(NULL);
        return;
    }
    
    ESP_LOGI(TAG, "Advertising started - device is now discoverable");
    
    /* Create update timer */
    update_timer = xTimerCreate("update_timer", 
                               pdMS_TO_TICKS(UPDATE_INTERVAL_MS),
                               pdTRUE,  // Auto-reload
                               NULL,
                               update_timer_callback);
    
    if (update_timer == NULL) {
        ESP_LOGE(TAG, "Failed to create update timer");
        stopadv();
        linkpet_deinit();
        vTaskDelete(NULL);
        return;
    }
    
    /* Start the update timer */
    xTimerStart(update_timer, 0);
    
    ESP_LOGI(TAG, "LinkPet Tracker Demo running - updates every %d seconds", 
             UPDATE_INTERVAL_MS / 1000);
    
    /* Main loop */
    while (1) {
        /* Check if advertising is still active */
        if (!linkpet_is_advertising()) {
            ESP_LOGW(TAG, "Advertising stopped, restarting...");
            err = startadv(&adv_params);
            if (err != LINKPET_OK) {
                ESP_LOGE(TAG, "Failed to restart advertising: %s", linkpet_err_to_str(err));
            }
        }
        
        /* Sleep for 5 seconds */
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
}

/**
 * @brief Application entry point
 */
void app_main(void)
{
    /* Create the main application task */
    xTaskCreatePinnedToCore(linkpet_tracker_task, "linkpet_tracker", 
                           8192, NULL, 5, NULL, 1);
}
