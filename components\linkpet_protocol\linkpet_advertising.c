#include "linkpet_internal.h"
#include "esp_log.h"
#include "host/ble_hs.h"
#include "host/ble_gap.h"
#include "host/util/util.h"
#include <string.h>

static const char *TAG = "LINKPET_ADV";

static int linkpet_gap_event(struct ble_gap_event *event, void *arg);

linkpet_err_t linkpet_advertising_init(void)
{
    ESP_LOGI(TAG, "Advertising module initialized");
    return LINKPET_OK;
}

linkpet_err_t linkpet_advertising_deinit(void)
{
    linkpet_state_t *state = linkpet_get_state();
    
    if (state->advertising) {
        ble_gap_adv_stop();
        state->advertising = false;
    }
    
    ESP_LOGI(TAG, "Advertising module deinitialized");
    return LINKPET_OK;
}

linkpet_err_t linkpet_build_adv_data(const linkpet_device_info_t *info, uint8_t *adv_data, 
                                     size_t max_len, size_t *actual_len)
{
    if (info == NULL || adv_data == NULL || actual_len == NULL) {
        return LINKPET_ERR_INVALID_ARG;
    }
    
    char json_str[LINKPET_MAX_JSON_LEN];
    linkpet_err_t err = linkpet_info_to_json(info, json_str, sizeof(json_str));
    if (err != LINKPET_OK) {
        return err;
    }
    
    size_t json_len = strlen(json_str);
    size_t required_len = 2 + json_len; // 2 bytes for length and type
    
    if (required_len > max_len) {
        return LINKPET_ERR_INVALID_ARG;
    }
    
    /* Build advertising data with manufacturer specific data */
    adv_data[0] = json_len + 1; // Length
    adv_data[1] = BLE_HS_ADV_TYPE_MFG_DATA; // Type: Manufacturer Specific Data
    memcpy(&adv_data[2], json_str, json_len);
    
    *actual_len = required_len;
    
    ESP_LOGI(TAG, "Built advertising data: %d bytes", (int)*actual_len);
    return LINKPET_OK;
}

linkpet_err_t linkpet_parse_adv_data(const uint8_t *adv_data, size_t adv_len, 
                                     linkpet_device_info_t *info)
{
    if (adv_data == NULL || info == NULL || adv_len == 0) {
        return LINKPET_ERR_INVALID_ARG;
    }
    
    size_t offset = 0;
    
    while (offset < adv_len) {
        if (offset + 1 >= adv_len) break;
        
        uint8_t length = adv_data[offset];
        uint8_t type = adv_data[offset + 1];
        
        if (length == 0 || offset + length >= adv_len) break;
        
        if (type == BLE_HS_ADV_TYPE_MFG_DATA && length > 1) {
            /* Found manufacturer specific data, try to parse as JSON */
            char json_str[LINKPET_MAX_JSON_LEN];
            size_t json_len = length - 1;
            
            if (json_len < sizeof(json_str)) {
                memcpy(json_str, &adv_data[offset + 2], json_len);
                json_str[json_len] = '\0';
                
                linkpet_err_t err = linkpet_json_to_info(json_str, info);
                if (err == LINKPET_OK) {
                    return LINKPET_OK;
                }
            }
        }
        
        offset += length + 1;
    }
    
    return LINKPET_ERR_NOT_FOUND;
}

static int linkpet_gap_event(struct ble_gap_event *event, void *arg)
{
    linkpet_state_t *state = linkpet_get_state();
    
    switch (event->type) {
    case BLE_GAP_EVENT_ADV_COMPLETE:
        ESP_LOGI(TAG, "Advertising complete; reason=%d", event->adv_complete.reason);
        state->advertising = false;
        break;
        
    case BLE_GAP_EVENT_CONNECT:
        ESP_LOGI(TAG, "Connection %s; status=%d",
                event->connect.status == 0 ? "established" : "failed",
                event->connect.status);
        
        if (event->connect.status == 0) {
            state->conn_handle = event->connect.conn_handle;
        }
        break;
        
    case BLE_GAP_EVENT_DISCONNECT:
        ESP_LOGI(TAG, "Disconnect; reason=%d", event->disconnect.reason);
        state->conn_handle = BLE_HS_CONN_HANDLE_NONE;
        
        /* Restart advertising if it was active */
        if (state->advertising) {
            startadv(NULL);
        }
        break;
        
    default:
        break;
    }
    
    return 0;
}

static linkpet_err_t validate_adv_params(const linkpet_adv_params_t *params)
{
    if (params == NULL) {
        return LINKPET_OK; // NULL params means use defaults
    }

    /* Validate advertising intervals (units of 0.625ms) */
    if (params->adv_int_min < 32 || params->adv_int_min > 16384) {
        ESP_LOGE(TAG, "Invalid min advertising interval: %d", params->adv_int_min);
        return LINKPET_ERR_INVALID_ARG;
    }

    if (params->adv_int_max < 32 || params->adv_int_max > 16384) {
        ESP_LOGE(TAG, "Invalid max advertising interval: %d", params->adv_int_max);
        return LINKPET_ERR_INVALID_ARG;
    }

    if (params->adv_int_min > params->adv_int_max) {
        ESP_LOGE(TAG, "Min interval (%d) > max interval (%d)",
                params->adv_int_min, params->adv_int_max);
        return LINKPET_ERR_INVALID_ARG;
    }

    /* Note: TX power validation skipped as setting is not available in this NimBLE version */
    if (params->tx_power < -20 || params->tx_power > 20) {
        ESP_LOGW(TAG, "TX power %d dBm out of typical range, but will use default", params->tx_power);
    }

    return LINKPET_OK;
}

linkpet_err_t startadv(const linkpet_adv_params_t *params)
{
    linkpet_state_t *state = linkpet_get_state();

    if (!state->initialized) {
        ESP_LOGE(TAG, "LinkPet protocol not initialized");
        return LINKPET_ERR_INVALID_STATE;
    }

    if (state->advertising) {
        ESP_LOGW(TAG, "Advertising already active");
        return LINKPET_ERR_ADVERTISING_ACTIVE;
    }

    /* Validate advertising parameters */
    linkpet_err_t err = validate_adv_params(params);
    if (err != LINKPET_OK) {
        return err;
    }

    /* Use provided parameters or defaults */
    const linkpet_adv_params_t *adv_params = params ? params : &state->adv_params;

    /* Validate device info before building advertising data */
    if (strlen(state->current_info.device_id) == 0 ||
        strlen(state->current_info.device_name) == 0) {
        ESP_LOGE(TAG, "Device info not properly set");
        return LINKPET_ERR_INVALID_STATE;
    }

    /* Build advertising data */
    err = linkpet_build_adv_data(&state->current_info, state->adv_data,
                                LINKPET_MAX_ADV_DATA_LEN, &state->adv_data_len);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to build advertising data: %s", linkpet_err_to_str(err));
        return err;
    }

    /* Validate advertising data size */
    if (state->adv_data_len > 31) {
        ESP_LOGE(TAG, "Advertising data too large: %d bytes", (int)state->adv_data_len);
        return LINKPET_ERR_INVALID_ARG;
    }

    /* Configure advertising parameters */
    struct ble_gap_adv_params gap_adv_params = {0};
    gap_adv_params.conn_mode = adv_params->connectable ? BLE_GAP_CONN_MODE_UND : BLE_GAP_CONN_MODE_NON;
    gap_adv_params.disc_mode = adv_params->scannable ? BLE_GAP_DISC_MODE_GEN : BLE_GAP_DISC_MODE_NON;
    gap_adv_params.itvl_min = adv_params->adv_int_min;
    gap_adv_params.itvl_max = adv_params->adv_int_max;
    gap_adv_params.channel_map = 0;
    gap_adv_params.filter_policy = 0;
    gap_adv_params.high_duty_cycle = 0;

    /* Note: TX power setting not available in this NimBLE version */
    /* The advertising will use default TX power */
    ESP_LOGI(TAG, "Using default TX power (TX power setting not available)");

    /* Start advertising */
    int rc = ble_gap_adv_set_data(state->adv_data, state->adv_data_len);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to set advertising data (rc=%d): %s", rc,
                rc == BLE_HS_EBUSY ? "BLE busy" : "Unknown error");
        return LINKPET_ERR_BLE_INIT;
    }

    rc = ble_gap_adv_start(BLE_OWN_ADDR_PUBLIC, NULL, BLE_HS_FOREVER,
                          &gap_adv_params, linkpet_gap_event, NULL);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to start advertising (rc=%d): %s", rc,
                rc == BLE_HS_EALREADY ? "Already advertising" :
                rc == BLE_HS_EINVAL ? "Invalid parameters" :
                rc == BLE_HS_EBUSY ? "BLE busy" : "Unknown error");
        return LINKPET_ERR_BLE_INIT;
    }

    state->advertising = true;
    state->adv_params = *adv_params;

    ESP_LOGI(TAG, "Advertising started: interval=%d-%dms, tx_power=%ddBm, connectable=%s",
            (adv_params->adv_int_min * 625) / 1000, (adv_params->adv_int_max * 625) / 1000,
            adv_params->tx_power, adv_params->connectable ? "yes" : "no");
    return LINKPET_OK;
}

linkpet_err_t stopadv(void)
{
    linkpet_state_t *state = linkpet_get_state();
    
    if (!state->initialized) {
        return LINKPET_ERR_INVALID_STATE;
    }
    
    if (!state->advertising) {
        return LINKPET_ERR_ADVERTISING_INACTIVE;
    }
    
    int rc = ble_gap_adv_stop();
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to stop advertising: %d", rc);
        return LINKPET_ERR_BLE_INIT;
    }
    
    state->advertising = false;
    
    ESP_LOGI(TAG, "Advertising stopped");
    return LINKPET_OK;
}
