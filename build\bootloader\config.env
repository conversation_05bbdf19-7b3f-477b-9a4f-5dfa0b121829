{"COMPONENT_KCONFIGS": "D:/Espressif/frameworks/esp-idf-v5.4.2/components/efuse/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_common/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_security/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/log/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/newlib/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/soc/Kconfig;D:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_app_format/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_rom/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esptool_py/Kconfig.projbuild;D:/Espressif/frameworks/esp-idf-v5.4.2/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "D:/Espressif/frameworks/esp-idf-v5.4.2/components/bootloader/sdkconfig.rename;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_hw_support/sdkconfig.rename;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esp_system/sdkconfig.rename;D:/Espressif/frameworks/esp-idf-v5.4.2/components/esptool_py/sdkconfig.rename;D:/Espressif/frameworks/esp-idf-v5.4.2/components/freertos/sdkconfig.rename;D:/Espressif/frameworks/esp-idf-v5.4.2/components/hal/sdkconfig.rename;D:/Espressif/frameworks/esp-idf-v5.4.2/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32c6", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.4.2", "IDF_ENV_FPGA": "", "IDF_PATH": "D:/Espressif/frameworks/esp-idf-v5.4.2", "COMPONENT_KCONFIGS_SOURCE_FILE": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader/kconfigs_projbuild.in"}