#include <stdio.h>
#include "user_app.h"
#include "sdcard_bsp.h"
#include "linkpet_protocol.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/timers.h"

//#define sdcard_write_Test

static const char *TAG = "USER_APP";

void sdcard_loop_task(void *arg);
void linkpet_demo_task(void *arg);

void user_app_init(void)
{
    ESP_LOGI(TAG, "Initializing user application");

    /* Initialize SD card */
    sdcard_init();

    /* Create SD card task */
    xTaskCreatePinnedToCore(sdcard_loop_task, "sdcard_loop_task", 5 * 1024, NULL, 2, NULL, 0);

    /* Create LinkPet demo task */
    xTaskCreatePinnedToCore(linkpet_demo_task, "linkpet_demo_task", 8 * 1024, NULL, 3, NULL, 1);

    ESP_LOGI(TAG, "User application initialized");
}


void sdcard_loop_task(void *arg)
{
    uint32_t value = 1;
    char test[45] = {""};
    char rtest[45] = {""};

    for(;;) {
#ifdef sdcard_write_Test
        snprintf(test, 45, "sdcard_writeTest : %ld\n", value);
        s_example_write_file("/sdcard/writeTest.txt", test);
        vTaskDelay(pdMS_TO_TICKS(500));
        s_example_read_file("/sdcard/writeTest.txt", rtest, NULL);
        printf("rtest:%s\n", rtest);
        vTaskDelay(pdMS_TO_TICKS(500));
        value++;
#else
        vTaskDelay(pdMS_TO_TICKS(5000));  // Reduced frequency
#endif
    }
}

static void scan_callback(const linkpet_scan_result_t *result, void *user_data)
{
    ESP_LOGI(TAG, "Found LinkPet device:");
    ESP_LOGI(TAG, "  Name: %s", result->info.device_name);
    ESP_LOGI(TAG, "  ID: %s", result->info.device_id);
    ESP_LOGI(TAG, "  Type: %s", result->info.device_type);
    ESP_LOGI(TAG, "  Status: %s", result->info.status);
    ESP_LOGI(TAG, "  Battery: %d%%", result->info.battery_level);
    ESP_LOGI(TAG, "  RSSI: %d dBm", result->rssi);
    ESP_LOGI(TAG, "  Location: %.6f, %.6f", result->info.latitude, result->info.longitude);

    if (strlen(result->info.custom_data) > 0) {
        ESP_LOGI(TAG, "  Custom Data: %s", result->info.custom_data);
    }
}

static void gatt_update_callback(const linkpet_device_info_t *info, void *user_data)
{
    ESP_LOGI(TAG, "Device info updated via GATT:");
    ESP_LOGI(TAG, "  Name: %s", info->device_name);
    ESP_LOGI(TAG, "  Status: %s", info->status);
    ESP_LOGI(TAG, "  Battery: %d%%", info->battery_level);

    /* Save updated info to SD card if available */
#ifdef sdcard_write_Test
    char json_str[256];
    linkpet_err_t err = linkpet_info_to_json(info, json_str, sizeof(json_str));
    if (err == LINKPET_OK) {
        s_example_write_file("/sdcard/linkpet_config.json", json_str);
        ESP_LOGI(TAG, "Configuration saved to SD card");
    }
#endif
}

void linkpet_demo_task(void *arg)
{
    ESP_LOGI(TAG, "Starting LinkPet demo task");

    /* Wait for system to stabilize */
    vTaskDelay(pdMS_TO_TICKS(2000));

    /* Set GATT callback */
    linkpet_err_t err = linkpet_set_gatt_callback(gatt_update_callback, NULL);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to set GATT callback: %s", linkpet_err_to_str(err));
    }

    /* Demo loop */
    uint32_t loop_count = 0;

    while (1) {
        loop_count++;

        /* Every 60 seconds, perform a scan for other LinkPet devices */
        if (loop_count % 12 == 0) { // Every 12 * 5 seconds = 60 seconds
            ESP_LOGI(TAG, "Scanning for other LinkPet devices...");

            /* Temporarily stop advertising to scan */
            if (linkpet_is_advertising()) {
                stopadv();
            }

            /* Scan for 10 seconds */
            err = scanlinkpet(10000, scan_callback, NULL);
            if (err != LINKPET_OK) {
                ESP_LOGE(TAG, "Failed to start scan: %s", linkpet_err_to_str(err));
            } else {
                /* Wait for scan to complete */
                vTaskDelay(pdMS_TO_TICKS(11000));
            }

            /* Restart advertising */
            err = startadv(NULL);
            if (err != LINKPET_OK) {
                ESP_LOGE(TAG, "Failed to restart advertising: %s", linkpet_err_to_str(err));
            }
        }

        /* Update device info periodically */
        if (loop_count % 6 == 0) { // Every 30 seconds
            linkpet_device_info_t current_info;
            err = linkpet_get_info(&current_info);
            if (err == LINKPET_OK) {
                /* Simulate battery drain */
                if (current_info.battery_level > 0) {
                    current_info.battery_level--;
                }

                /* Update timestamp */
                current_info.timestamp = esp_timer_get_time() / 1000000;

                /* Update status based on battery */
                if (current_info.battery_level <= 10) {
                    strcpy(current_info.status, "low_battery");
                } else if (current_info.battery_level <= 30) {
                    strcpy(current_info.status, "battery_warning");
                } else {
                    strcpy(current_info.status, "active");
                }

                /* Apply updates */
                err = setinfo(&current_info);
                if (err == LINKPET_OK) {
                    ESP_LOGI(TAG, "Device info updated - Battery: %d%%, Status: %s",
                            current_info.battery_level, current_info.status);
                }
            }
        }

        /* Sleep for 5 seconds */
        vTaskDelay(pdMS_TO_TICKS(5000));
    }
}




