#include <stdio.h>
#include "user_app.h"
#include "linkpet_protocol.h"
#include "esp_log.h"
#include "nvs_flash.h"

static const char *TAG = "MAIN";

void app_main(void)
{
    ESP_LOGI(TAG, "Starting LinkPet Protocol Demo");

    /* Initialize NVS */
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    /* Initialize user app (SD card functionality) */
    user_app_init();

    /* Initialize LinkPet protocol */
    linkpet_err_t err = linkpet_init();
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to initialize LinkPet protocol: %s", linkpet_err_to_str(err));
        return;
    }

    ESP_LOGI(TAG, "LinkPet protocol initialized successfully");

    /* Set up basic device information */
    linkpet_device_info_t info = {
        .device_id = "linkpet_demo_001",
        .device_name = "LinkPet Demo Device",
        .device_type = "tracker",
        .status = "active",
        .battery_level = 100,
        .latitude = 37.7749,   // San Francisco
        .longitude = -122.4194,
        .timestamp = 0,
        .custom_data = "{\"demo\":true,\"version\":\"1.0\"}"
    };

    err = setinfo(&info);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to set device info: %s", linkpet_err_to_str(err));
        return;
    }

    /* Start advertising with default parameters */
    err = startadv(NULL);
    if (err != LINKPET_OK) {
        ESP_LOGE(TAG, "Failed to start advertising: %s", linkpet_err_to_str(err));
        return;
    }

    ESP_LOGI(TAG, "LinkPet advertising started - device is discoverable");
    ESP_LOGI(TAG, "Device ID: %s", info.device_id);
    ESP_LOGI(TAG, "Device Name: %s", info.device_name);
    ESP_LOGI(TAG, "Use a BLE scanner or LinkPet app to discover this device");
}
