#include "linkpet_protocol.h"
#include "linkpet_internal.h"
#include "esp_log.h"
#include "esp_nimble_hci.h"
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include <string.h>

static const char *TAG = "LINKPET_PROTOCOL";

/* Global state */
static linkpet_state_t g_linkpet_state = {0};
static SemaphoreHandle_t g_linkpet_mutex = NULL;

/* Default advertising parameters */
static const linkpet_adv_params_t default_adv_params = {
    .adv_int_min = 160,    // 100ms
    .adv_int_max = 160,    // 100ms
    .tx_power = 0,         // 0 dBm
    .connectable = true,
    .scannable = true
};

/* Error code to string mapping */
static const char* error_strings[] = {
    "Success",
    "Invalid argument",
    "Out of memory", 
    "Invalid state",
    "Device not found",
    "Operation timeout",
    "Advertising already active",
    "Advertising not active",
    "JSON parsing error",
    "Invalid JSON format",
    "BLE initialization error",
    "GATT operation error"
};

linkpet_state_t* linkpet_get_state(void)
{
    return &g_linkpet_state;
}

const char* linkpet_err_to_str(linkpet_err_t err)
{
    if (err >= 0 && err < sizeof(error_strings)/sizeof(error_strings[0])) {
        return error_strings[err];
    }
    return "Unknown error";
}

static void linkpet_host_task(void *param)
{
    ESP_LOGI(TAG, "BLE Host Task Started");
    nimble_port_run();
    nimble_port_freertos_deinit();
}

static void linkpet_on_reset(int reason)
{
    ESP_LOGI(TAG, "Resetting state; reason=%d", reason);
}

static void linkpet_on_sync(void)
{
    int rc;
    
    ESP_LOGI(TAG, "BLE Host synchronized");
    
    /* Make sure we have proper identity address set (public preferred) */
    rc = ble_hs_util_ensure_addr(0);
    assert(rc == 0);
    
    /* Initialize sub-modules */
    linkpet_advertising_init();
    linkpet_gatt_init();
    linkpet_scanner_init();
}

linkpet_err_t linkpet_init(void)
{
    esp_err_t ret;
    
    if (g_linkpet_state.initialized) {
        ESP_LOGW(TAG, "LinkPet protocol already initialized");
        return LINKPET_OK;
    }
    
    /* Create mutex for thread safety */
    g_linkpet_mutex = xSemaphoreCreateMutex();
    if (g_linkpet_mutex == NULL) {
        ESP_LOGE(TAG, "Failed to create mutex");
        return LINKPET_ERR_NO_MEM;
    }
    
    /* Initialize NimBLE */
    ret = esp_nimble_hci_and_controller_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "esp_nimble_hci_and_controller_init() failed with error: %d", ret);
        vSemaphoreDelete(g_linkpet_mutex);
        return LINKPET_ERR_BLE_INIT;
    }
    
    nimble_port_init();
    
    /* Configure the host */
    ble_hs_cfg.reset_cb = linkpet_on_reset;
    ble_hs_cfg.sync_cb = linkpet_on_sync;
    ble_hs_cfg.gatts_register_cb = NULL;
    ble_hs_cfg.store_status_cb = NULL;
    
    /* Set device name */
    ret = ble_svc_gap_device_name_set(LINKPET_DEVICE_NAME);
    if (ret != 0) {
        ESP_LOGE(TAG, "Failed to set device name; rc=%d", ret);
        esp_nimble_hci_and_controller_deinit();
        vSemaphoreDelete(g_linkpet_mutex);
        return LINKPET_ERR_BLE_INIT;
    }
    
    /* Initialize default device info */
    memset(&g_linkpet_state.current_info, 0, sizeof(linkpet_device_info_t));
    strcpy(g_linkpet_state.current_info.device_id, "linkpet_default");
    strcpy(g_linkpet_state.current_info.device_name, LINKPET_DEVICE_NAME);
    strcpy(g_linkpet_state.current_info.device_type, "tracker");
    strcpy(g_linkpet_state.current_info.status, "active");
    g_linkpet_state.current_info.battery_level = 100;
    g_linkpet_state.current_info.timestamp = 0;
    
    /* Set default advertising parameters */
    g_linkpet_state.adv_params = default_adv_params;
    
    /* Start the host task */
    nimble_port_freertos_init(linkpet_host_task);
    
    g_linkpet_state.initialized = true;
    
    ESP_LOGI(TAG, "LinkPet protocol initialized successfully");
    return LINKPET_OK;
}

linkpet_err_t linkpet_deinit(void)
{
    if (!g_linkpet_state.initialized) {
        return LINKPET_ERR_INVALID_STATE;
    }
    
    /* Stop advertising if active */
    if (g_linkpet_state.advertising) {
        stopadv();
    }
    
    /* Deinitialize sub-modules */
    linkpet_advertising_deinit();
    linkpet_gatt_deinit();
    linkpet_scanner_deinit();
    
    /* Deinitialize NimBLE */
    esp_nimble_hci_and_controller_deinit();
    
    /* Clean up mutex */
    if (g_linkpet_mutex) {
        vSemaphoreDelete(g_linkpet_mutex);
        g_linkpet_mutex = NULL;
    }
    
    memset(&g_linkpet_state, 0, sizeof(linkpet_state_t));
    
    ESP_LOGI(TAG, "LinkPet protocol deinitialized");
    return LINKPET_OK;
}

static linkpet_err_t validate_device_info(const linkpet_device_info_t *info)
{
    if (info == NULL) {
        return LINKPET_ERR_INVALID_ARG;
    }

    /* Validate device_id */
    if (strlen(info->device_id) == 0) {
        ESP_LOGE(TAG, "Device ID cannot be empty");
        return LINKPET_ERR_INVALID_ARG;
    }

    /* Validate device_name */
    if (strlen(info->device_name) == 0) {
        ESP_LOGE(TAG, "Device name cannot be empty");
        return LINKPET_ERR_INVALID_ARG;
    }

    /* Validate battery level */
    if (info->battery_level > 100) {
        ESP_LOGE(TAG, "Battery level out of range: %d", info->battery_level);
        return LINKPET_ERR_INVALID_ARG;
    }

    /* Validate GPS coordinates */
    if (info->latitude < -90.0 || info->latitude > 90.0) {
        ESP_LOGE(TAG, "Latitude out of range: %.6f", info->latitude);
        return LINKPET_ERR_INVALID_ARG;
    }

    if (info->longitude < -180.0 || info->longitude > 180.0) {
        ESP_LOGE(TAG, "Longitude out of range: %.6f", info->longitude);
        return LINKPET_ERR_INVALID_ARG;
    }

    return LINKPET_OK;
}

linkpet_err_t setinfo(const linkpet_device_info_t *info)
{
    if (!g_linkpet_state.initialized) {
        ESP_LOGE(TAG, "LinkPet protocol not initialized");
        return LINKPET_ERR_INVALID_STATE;
    }

    /* Validate input */
    linkpet_err_t err = validate_device_info(info);
    if (err != LINKPET_OK) {
        return err;
    }

    if (xSemaphoreTake(g_linkpet_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to acquire mutex for setinfo");
        return LINKPET_ERR_TIMEOUT;
    }

    /* Update current info */
    memcpy(&g_linkpet_state.current_info, info, sizeof(linkpet_device_info_t));

    /* If advertising is active, update advertising data */
    if (g_linkpet_state.advertising) {
        err = linkpet_build_adv_data(info, g_linkpet_state.adv_data,
                                    LINKPET_MAX_ADV_DATA_LEN,
                                    &g_linkpet_state.adv_data_len);
        if (err != LINKPET_OK) {
            ESP_LOGE(TAG, "Failed to build advertising data: %s", linkpet_err_to_str(err));
            xSemaphoreGive(g_linkpet_mutex);
            return err;
        }

        /* Restart advertising with new data */
        ESP_LOGI(TAG, "Restarting advertising with updated data");
        stopadv();
        err = startadv(NULL);
        if (err != LINKPET_OK) {
            ESP_LOGE(TAG, "Failed to restart advertising: %s", linkpet_err_to_str(err));
            xSemaphoreGive(g_linkpet_mutex);
            return err;
        }
    }

    xSemaphoreGive(g_linkpet_mutex);

    ESP_LOGI(TAG, "Device info updated successfully: %s", info->device_name);
    return LINKPET_OK;
}

linkpet_err_t linkpet_get_info(linkpet_device_info_t *info)
{
    if (!g_linkpet_state.initialized) {
        return LINKPET_ERR_INVALID_STATE;
    }
    
    if (info == NULL) {
        return LINKPET_ERR_INVALID_ARG;
    }
    
    if (xSemaphoreTake(g_linkpet_mutex, pdMS_TO_TICKS(1000)) != pdTRUE) {
        return LINKPET_ERR_TIMEOUT;
    }
    
    memcpy(info, &g_linkpet_state.current_info, sizeof(linkpet_device_info_t));
    
    xSemaphoreGive(g_linkpet_mutex);
    return LINKPET_OK;
}

bool linkpet_is_advertising(void)
{
    return g_linkpet_state.advertising;
}

linkpet_err_t linkpet_set_gatt_callback(linkpet_gatt_update_cb_t callback, void *user_data)
{
    if (!g_linkpet_state.initialized) {
        return LINKPET_ERR_INVALID_STATE;
    }

    g_linkpet_state.gatt_callback = callback;
    g_linkpet_state.gatt_user_data = user_data;

    return LINKPET_OK;
}

linkpet_err_t linkpet_info_to_json(const linkpet_device_info_t *info, char *json_str, size_t max_len)
{
    if (info == NULL || json_str == NULL || max_len == 0) {
        return LINKPET_ERR_INVALID_ARG;
    }

    cJSON *json = cJSON_CreateObject();
    if (json == NULL) {
        return LINKPET_ERR_NO_MEM;
    }

    cJSON_AddStringToObject(json, "device_id", info->device_id);
    cJSON_AddStringToObject(json, "device_name", info->device_name);
    cJSON_AddStringToObject(json, "device_type", info->device_type);
    cJSON_AddStringToObject(json, "status", info->status);
    cJSON_AddNumberToObject(json, "battery_level", info->battery_level);
    cJSON_AddNumberToObject(json, "latitude", info->latitude);
    cJSON_AddNumberToObject(json, "longitude", info->longitude);
    cJSON_AddNumberToObject(json, "timestamp", (double)info->timestamp);

    if (strlen(info->custom_data) > 0) {
        cJSON *custom = cJSON_Parse(info->custom_data);
        if (custom != NULL) {
            cJSON_AddItemToObject(json, "custom_data", custom);
        } else {
            cJSON_AddStringToObject(json, "custom_data", info->custom_data);
        }
    }

    char *json_string = cJSON_Print(json);
    if (json_string == NULL) {
        cJSON_Delete(json);
        return LINKPET_ERR_NO_MEM;
    }

    if (strlen(json_string) >= max_len) {
        free(json_string);
        cJSON_Delete(json);
        return LINKPET_ERR_INVALID_ARG;
    }

    strcpy(json_str, json_string);
    free(json_string);
    cJSON_Delete(json);

    return LINKPET_OK;
}

linkpet_err_t linkpet_json_to_info(const char *json_str, linkpet_device_info_t *info)
{
    if (json_str == NULL || info == NULL) {
        ESP_LOGE(TAG, "Invalid arguments: json_str=%p, info=%p", json_str, info);
        return LINKPET_ERR_INVALID_ARG;
    }

    /* Validate JSON string length */
    size_t json_len = strlen(json_str);
    if (json_len == 0 || json_len >= LINKPET_MAX_JSON_LEN) {
        ESP_LOGE(TAG, "Invalid JSON string length: %d", (int)json_len);
        return LINKPET_ERR_JSON_FORMAT;
    }

    cJSON *json = cJSON_Parse(json_str);
    if (json == NULL) {
        const char *error_ptr = cJSON_GetErrorPtr();
        ESP_LOGE(TAG, "JSON parse error at: %s", error_ptr ? error_ptr : "unknown");
        return LINKPET_ERR_JSON_PARSE;
    }

    /* Validate that it's a JSON object */
    if (!cJSON_IsObject(json)) {
        ESP_LOGE(TAG, "JSON is not an object");
        cJSON_Delete(json);
        return LINKPET_ERR_JSON_FORMAT;
    }

    memset(info, 0, sizeof(linkpet_device_info_t));

    /* Parse and validate device_id (required) */
    cJSON *item = cJSON_GetObjectItem(json, "device_id");
    if (!cJSON_IsString(item) || strlen(item->valuestring) == 0) {
        ESP_LOGE(TAG, "Missing or invalid device_id");
        cJSON_Delete(json);
        return LINKPET_ERR_JSON_FORMAT;
    }
    strncpy(info->device_id, item->valuestring, sizeof(info->device_id) - 1);

    /* Parse device_name (required) */
    item = cJSON_GetObjectItem(json, "device_name");
    if (!cJSON_IsString(item) || strlen(item->valuestring) == 0) {
        ESP_LOGE(TAG, "Missing or invalid device_name");
        cJSON_Delete(json);
        return LINKPET_ERR_JSON_FORMAT;
    }
    strncpy(info->device_name, item->valuestring, sizeof(info->device_name) - 1);

    /* Parse device_type (optional, default to "tracker") */
    item = cJSON_GetObjectItem(json, "device_type");
    if (cJSON_IsString(item) && strlen(item->valuestring) > 0) {
        strncpy(info->device_type, item->valuestring, sizeof(info->device_type) - 1);
    } else {
        strcpy(info->device_type, "tracker");
    }

    /* Parse status (optional, default to "active") */
    item = cJSON_GetObjectItem(json, "status");
    if (cJSON_IsString(item) && strlen(item->valuestring) > 0) {
        strncpy(info->status, item->valuestring, sizeof(info->status) - 1);
    } else {
        strcpy(info->status, "active");
    }

    /* Parse battery_level (optional, validate range 0-100) */
    item = cJSON_GetObjectItem(json, "battery_level");
    if (cJSON_IsNumber(item)) {
        double battery = item->valuedouble;
        if (battery < 0 || battery > 100) {
            ESP_LOGW(TAG, "Battery level out of range: %.1f, clamping to 0-100", battery);
            battery = battery < 0 ? 0 : 100;
        }
        info->battery_level = (uint8_t)battery;
    } else {
        info->battery_level = 100; // Default to full battery
    }

    /* Parse latitude (optional, validate range) */
    item = cJSON_GetObjectItem(json, "latitude");
    if (cJSON_IsNumber(item)) {
        double lat = item->valuedouble;
        if (lat < -90.0 || lat > 90.0) {
            ESP_LOGW(TAG, "Latitude out of range: %.6f", lat);
            info->latitude = 0.0;
        } else {
            info->latitude = (float)lat;
        }
    }

    /* Parse longitude (optional, validate range) */
    item = cJSON_GetObjectItem(json, "longitude");
    if (cJSON_IsNumber(item)) {
        double lon = item->valuedouble;
        if (lon < -180.0 || lon > 180.0) {
            ESP_LOGW(TAG, "Longitude out of range: %.6f", lon);
            info->longitude = 0.0;
        } else {
            info->longitude = (float)lon;
        }
    }

    /* Parse timestamp (optional) */
    item = cJSON_GetObjectItem(json, "timestamp");
    if (cJSON_IsNumber(item)) {
        double timestamp = item->valuedouble;
        if (timestamp >= 0) {
            info->timestamp = (uint64_t)timestamp;
        }
    }

    /* Parse custom_data (optional) */
    item = cJSON_GetObjectItem(json, "custom_data");
    if (item != NULL) {
        char *custom_str = cJSON_Print(item);
        if (custom_str != NULL) {
            size_t custom_len = strlen(custom_str);
            if (custom_len < sizeof(info->custom_data)) {
                strcpy(info->custom_data, custom_str);
            } else {
                ESP_LOGW(TAG, "Custom data too large, truncating");
                strncpy(info->custom_data, custom_str, sizeof(info->custom_data) - 1);
            }
            free(custom_str);
        }
    }

    cJSON_Delete(json);
    ESP_LOGI(TAG, "Successfully parsed device info: %s", info->device_name);
    return LINKPET_OK;
}
