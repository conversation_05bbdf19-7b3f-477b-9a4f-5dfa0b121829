{"sources": [{"file": "E:/APPprj/LinkPet/linkpet-protocol/build/CMakeFiles/bootloader"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/CMakeFiles/bootloader.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/CMakeFiles/bootloader-complete.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "E:/APPprj/LinkPet/linkpet-protocol/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}